import React from 'react';

const isValidVideoUrl = (url) => {
  // Vimeo aur YouTube dono check karega
  const regex =
    /^(https?:\/\/)?(www\.)?(vimeo\.com\/\d+|youtube\.com\/watch\?v=[\w-]+|youtu\.be\/[\w-]+)$/;
  return regex.test(url);
};

const getEmbedUrl = (url) => {
  if (url.includes('vimeo.com')) {
    const videoId = url.split('/').pop();
    return `https://player.vimeo.com/video/${videoId}`;
  } else if (url.includes('youtube.com') || url.includes('youtu.be')) {
    let videoId = '';
    if (url.includes('youtube.com')) {
      const params = new URLSearchParams(url.split('?')[1]);
      videoId = params.get('v');
    } else {
      videoId = url.split('/').pop();
    }
    return `https://www.youtube.com/embed/${videoId}`;
  }
  return null;
};

const VideoEmbed = ({ url, title }) => {
  if (!isValidVideoUrl(url)) {
    return <p className="text-red-500">Invalid video link</p>;
  }

  const embedUrl = getEmbedUrl(url);

  return (
    <div className="video-container">
      <iframe
        src={embedUrl}
        title={title}
        frameBorder="0"
        allow="autoplay; fullscreen"
        allowFullScreen
        className="w-full h-64 rounded-lg"
      ></iframe>
    </div>
  );
};

export default VideoEmbed;
